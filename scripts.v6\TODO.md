# 插件配置管理模块 - 开发任务清单

## 项目概述

重写 `scripts/extension-config-manager/` 模块，实现一个全新的、健壮的浏览器扩展配置管理系统。

## 文件结构设计

```
scripts.v6/
├── extension-config/
│   ├── types.ts                # 核心类型定义和常量
│   ├── core.ts                 # 核心处理逻辑 (defineExtensionConfig + 验证 + 路径管理)
│   ├── utils.ts                # 辅助工具函数 (列表查询、清理等)
│   └── index.ts                # 主入口和 API 导出
├── i18n/
│   ├── types.ts                # i18n 相关类型定义
│   ├── processor.ts            # 国际化处理器 (扫描、合并、条件化解析、格式转换)
│   └── index.ts                # i18n 模块导出
├── manifest/
│   ├── types.ts                # manifest 相关类型定义
│   ├── processor.ts            # Manifest 配置处理器 (深度合并、权限分离、自动填充)
│   └── index.ts                # manifest 模块导出
└── shared/                     # 按需补充的共享工具
    └── logger.ts               # 已存在的日志工具
```

## 开发任务清单

### 阶段一：基础设施建设

- [x] **任务 0: 创建任务拆解文档** ✅
  - [x] 分析需求文档
  - [x] 重新设计文件结构（功能聚合，模块分离）
  - [x] 制定开发计划

- [ ] **任务 1: 实现 extension-config 模块** 🔄
  - [ ] `types.ts`: 核心类型系统和常量
    - [ ] 原始配置类型 (`ExtensionConfig`, `VariantConfig`, `I18nConfig`, `ManifestConfig`)
    - [ ] 处理后配置类型 (`ProcessedVariantConfig`, `ProcessedPaths`)
    - [ ] 验证相关类型 (`ValidationResult`)
    - [ ] 支持的浏览器商店、变体类型、Manifest 版本等常量
    - [ ] 各商店的更新 URL、扩展基础 URL 等常量映射
  - [ ] `core.ts`: 核心处理逻辑
    - [ ] `defineExtensionConfig()`: 配置定义的核心入口函数
    - [ ] 配置验证器：验证必填字段、枚举值、variantTarget 唯一性
    - [ ] 路径管理：统一的路径生成和管理功能
    - [ ] 配置扁平化与合并逻辑
  - [ ] `utils.ts`: 辅助工具函数
    - [ ] `listExtensionsByDir()`: 扫描扩展列表
    - [ ] `listExtensionVariants()`: 获取变体信息
    - [ ] `cleanExtensionConfigs()`: 清理缓存
    - [ ] `getProcessedExtensionConfig()`: 加载处理配置
  - [ ] `index.ts`: 主入口和 API 导出

- [ ] **任务 2: 实现 i18n 模块**
  - [ ] `types.ts`: i18n 相关类型定义
    - [ ] `ProcessedI18nConfig`, `LocaleMessages`, `ChromeMessage` 等
    - [ ] 引用 `SUPPORTED_LOCALES` from '@wxt-dev/i18n/build'
  - [ ] `processor.ts`: 国际化处理器
    - [ ] 自动扫描语言包目录
    - [ ] 语言包合并逻辑（扩展专属优先于共享）
    - [ ] 条件化文案解析 (`KEY@webstore-mv-variant` 格式)
    - [ ] 文案过滤 (includeKeys, excludeKeys)
    - [ ] 生成 Vue I18n 格式和 Chrome 格式
    - [ ] 支持 `chromeOnlyLocales` 和 `chromeOnlyKeys` 配置
  - [ ] `index.ts`: i18n 模块导出

- [ ] **任务 3: 实现 manifest 模块**
  - [ ] `types.ts`: manifest 相关类型定义
    - [ ] 引用 `Browser.runtime.Manifest` from 'wxt/browser'
    - [ ] 定义 `BaseManifestConfig`, `ManifestConfig` 等
  - [ ] `processor.ts`: Manifest 配置处理器
    - [ ] 深度合并逻辑（渠道包配置优先于顶层配置）
    - [ ] 权限分离 (permissions → permissions + host_permissions)
    - [ ] 自动填充字段 (version, manifest_version, default_locale, homepage_url)
    - [ ] 根据 webstore 生成 update_url
    - [ ] 处理数组字段的去重合并
  - [ ] `index.ts`: manifest 模块导出

### 阶段二：集成和文件生成

- [ ] **任务 4: 实现文件生成功能**
  - [ ] 在 `extension-config/core.ts` 中实现文件生成逻辑
  - [ ] 生成 `.variants/{variantTarget}/extension.config.json`
  - [ ] 调用 i18n 模块生成 `.variants/{variantTarget}/i18n.json` (Vue I18n 格式)
  - [ ] 调用 i18n 模块生成 `.variants/{variantTarget}/public/_locales/{locale}/messages.json` (Chrome 格式)
  - [ ] 生产模式下生成 `.manifests/manifest.{variantTarget}.json` 备份
  - [ ] 支持原子性写入，避免部分写入导致的不一致状态

- [ ] **任务 5: 模块集成和测试**
  - [ ] 完善 `extension-config/index.ts` 的 API 导出
  - [ ] 确保三个模块之间的正确集成
  - [ ] 实现 `getProcessedExtensionConfig()` 函数
  - [ ] 手动测试核心功能
  - [ ] 验证生成的文件格式和内容正确性

### 阶段三：文档和完善

- [ ] **任务 6: 编写使用文档**
  - [ ] API 参考文档：详细说明每个函数的参数、返回值、使用场景
  - [ ] 配置指南：`extension.config.ts` 的编写规范和最佳实践
  - [ ] 集成指南：如何在构建系统中使用该模块
  - [ ] 故障排除：常见问题和解决方案
  - [ ] 迁移指南：从旧版本迁移的步骤和注意事项

## 当前进度

- ✅ 需求分析和架构设计完成（重新规划）
- 🔄 正在进行：任务 1 - 实现 extension-config 模块
- ⏳ 待开始：任务 2-6

## 关键里程碑

1. **基础模块完成** (任务 1-3): 三个核心模块的独立实现
2. **集成功能完成** (任务 4-5): 文件生成和模块集成测试
3. **项目完成** (任务 6): 完整的使用文档和指南

## 设计亮点

- **功能聚合**: 相关功能在单个文件中实现，减少文件碎片
- **模块分离**: i18n 和 manifest 处理独立成模块，职责清晰
- **外部依赖**: 正确使用 '@wxt-dev/i18n/build' 和 'wxt/browser' 的类型定义
- **按需抽象**: 不过度设计，shared 模块按实际需要补充

## 验收标准

每个任务完成后需要满足：
1. ✅ 代码通过 TypeScript 编译检查
2. ✅ 核心功能通过手动测试验证
3. ✅ 代码符合项目的编程规范
4. ✅ 提供必要的中文注释和文档
5. ✅ 与现有系统的集成测试通过

## 注意事项

- 这是对现有模块的**重写**，不是重构，可以参考但不要照搬
- 严格遵循 KISS 和 YAGNI 原则
- 优先实现 P0 核心功能，再考虑 P1/P2 功能
- 每完成一个任务都要更新此文档的进度
