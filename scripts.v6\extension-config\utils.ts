/**
 * @fileoverview 插件配置管理模块的辅助工具函数
 * @description 实现扩展列表查询、变体信息获取、配置清理等辅助功能
 */

import fs from 'fs';
import path from 'path';
import { pathToFileURL } from 'url';
import { createLogger } from '../shared/logger.js';
import type { ProcessedVariantConfig, ExtensionConfig } from './types.js';

const logger = createLogger('ExtensionConfigUtils');

// #region --- 路径工具 ---

/**
 * @description 获取工作区根目录
 */
function getWorkspaceRoot(): string {
  return path.resolve(__dirname, '../../../');
}

/**
 * @description 获取扩展目录路径
 */
function getExtensionsDir(): string {
  return path.join(getWorkspaceRoot(), 'packages', 'extensions');
}

// #endregion

// #region --- 扩展发现和列表 ---

/**
 * @description 扫描并列出所有包含 extension.config.ts 的扩展
 * @param extensionNames 可选的扩展名称列表，如果提供则只处理指定的扩展
 * @returns 扩展名称列表
 */
export function listExtensionsByDir(extensionNames?: string[]): string[] {
  const extensionsDir = getExtensionsDir();
  
  if (!fs.existsSync(extensionsDir)) {
    logger.warn(`扩展目录不存在: ${extensionsDir}`);
    return [];
  }

  try {
    const allDirs = fs.readdirSync(extensionsDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);

    // 如果指定了扩展名称，则过滤
    const targetDirs = extensionNames ? allDirs.filter(name => extensionNames.includes(name)) : allDirs;

    // 检查每个目录是否包含 extension.config.ts
    const validExtensions = targetDirs.filter(dirName => {
      const configPath = path.join(extensionsDir, dirName, 'extension.config.ts');
      return fs.existsSync(configPath);
    });

    logger.verbose(`发现 ${validExtensions.length} 个有效扩展: ${validExtensions.join(', ')}`);
    return validExtensions;
  } catch (error) {
    logger.error(`扫描扩展目录失败: ${error}`);
    return [];
  }
}

/**
 * @description 获取指定扩展的所有变体信息
 * @param extensionName 扩展名称，如果未指定则列出所有扩展的变体
 * @returns 变体信息列表，格式为 { extensionName, variantTarget, webstore, variantType }
 */
export async function listExtensionVariants(extensionName?: string): Promise<Array<{
  extensionName: string;
  variantTarget: string;
  webstore: string;
  variantType: string;
  manifestVersion: number;
}>> {
  const extensions = extensionName ? [extensionName] : listExtensionsByDir();
  const variants: Array<{
    extensionName: string;
    variantTarget: string;
    webstore: string;
    variantType: string;
    manifestVersion: number;
  }> = [];

  for (const extName of extensions) {
    try {
      const configPath = path.join(getExtensionsDir(), extName, 'extension.config.ts');
      
      if (!fs.existsSync(configPath)) {
        logger.warn(`扩展配置文件不存在: ${configPath}`);
        continue;
      }

      // 动态导入配置文件
      const configModule = await import(pathToFileURL(configPath).href);
      const processedVariants: Record<string, ProcessedVariantConfig> = configModule.default || {};

      // 提取变体信息
      Object.values(processedVariants).forEach(variant => {
        variants.push({
          extensionName: extName,
          variantTarget: variant.variantTarget,
          webstore: variant.webstore,
          variantType: variant.variantType,
          manifestVersion: variant.manifestVersion,
        });
      });

      logger.verbose(`扩展 ${extName} 包含 ${Object.keys(processedVariants).length} 个变体`);
    } catch (error) {
      logger.error(`处理扩展 ${extName} 的配置时出错: ${error}`);
    }
  }

  return variants;
}

// #endregion

// #region --- 配置清理 ---

/**
 * @description 递归删除目录
 * @param dirPath 目录路径
 */
function removeDirectory(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    return;
  }

  try {
    fs.rmSync(dirPath, { recursive: true, force: true });
    logger.verbose(`已删除目录: ${dirPath}`);
  } catch (error) {
    logger.error(`删除目录失败 ${dirPath}: ${error}`);
  }
}

/**
 * @description 清理指定扩展的 .variants 缓存目录
 * @param extensionNames 可选的扩展名称列表，如果未指定则清理所有扩展
 */
export function cleanExtensionConfigs(extensionNames?: string[]): void {
  const extensions = extensionNames || listExtensionsByDir();
  let cleanedCount = 0;

  extensions.forEach(extensionName => {
    const extensionDir = path.join(getExtensionsDir(), extensionName);
    const variantsDir = path.join(extensionDir, '.variants');
    const manifestsDir = path.join(extensionDir, '.manifests');

    // 清理 .variants 目录
    if (fs.existsSync(variantsDir)) {
      removeDirectory(variantsDir);
      cleanedCount++;
    }

    // 清理 .manifests 目录
    if (fs.existsSync(manifestsDir)) {
      removeDirectory(manifestsDir);
    }
  });

  if (cleanedCount > 0) {
    logger.success(`已清理 ${cleanedCount} 个扩展的配置缓存`);
  } else {
    logger.info('没有找到需要清理的配置缓存');
  }
}

// #endregion

// #region --- 配置加载 ---

/**
 * @description 加载并处理指定插件的配置，可选择性地生成文件
 * @param extensionName 插件名称
 * @param options 处理选项
 * @returns 处理后的变体配置对象
 */
export async function getProcessedExtensionConfig(
  extensionName: string,
  options: {
    variantTargets?: string[];
    generateFiles?: boolean;
  } = {},
): Promise<Record<string, ProcessedVariantConfig>> {
  const configPath = path.join(getExtensionsDir(), extensionName, 'extension.config.ts');
  
  if (!fs.existsSync(configPath)) {
    throw new Error(`扩展配置文件不存在: ${configPath}`);
  }

  try {
    // 动态导入插件配置 - extension.config.ts 已经通过 defineExtensionConfig 处理过了
    const configModule = await import(pathToFileURL(configPath).href);
    const allProcessedVariants: Record<string, ProcessedVariantConfig> = configModule.default || {};

    // 如果指定了特定的 variantTargets，则过滤出指定的变体配置
    let filteredVariants = allProcessedVariants;
    if (options.variantTargets && options.variantTargets.length > 0) {
      filteredVariants = {};
      for (const target of options.variantTargets) {
        if (allProcessedVariants[target]) {
          filteredVariants[target] = allProcessedVariants[target];
        } else {
          logger.warn(`未找到变体目标: ${target}`);
        }
      }
    }

    // 如果需要生成配置文件和语言包到文件系统
    if (options.generateFiles) {
      // TODO: 这里将在任务 4 中实现文件生成功能
      logger.info('文件生成功能将在后续任务中实现');
    }

    logger.verbose(`成功加载扩展配置: ${extensionName}，包含 ${Object.keys(filteredVariants).length} 个变体`);
    return filteredVariants;
  } catch (error) {
    const errorMessage = `加载扩展配置失败 ${extensionName}: ${error}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }
}

// #endregion

// #region --- 配置模板 ---

/**
 * @description 返回一个基础配置模板，用于快速创建新的 extension.config.ts
 * @returns 基础扩展配置模板
 */
export function getBaseExtensionConfig(): ExtensionConfig {
  return {
    name: 'my-extension',
    version: '2.0.0',
    manifestVersion: 3,
    defaultLocale: 'en',
    i18n: {
      locales: [], // 会被自动扫描覆盖
      includeKeys: [],
      excludeKeys: [],
      chromeOnlyLocales: [],
      chromeOnlyKeys: [],
    },
    manifest: {
      name: '__MSG_EXTENSION_NAME__',
      description: '__MSG_EXTENSION_DESCRIPTION__',
      permissions: [],
    },
    variants: [
      // 示例变体配置（需要根据实际情况修改）
      // {
      //   variantId: 'my-variant-id',
      //   variantName: 'My Variant',
      //   variantType: 'master',
      //   webstore: 'chrome',
      // },
    ],
  };
}

// #endregion
