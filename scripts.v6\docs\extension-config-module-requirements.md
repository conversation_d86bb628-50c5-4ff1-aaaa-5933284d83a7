## 插件配置管理模块：需求规格说明

本模块旨在提供一套健壮、灵活且自动化的浏览器扩展配置管理系统。其核心目标是简化多渠道扩展的配置流程，确保配置的一致性、可维护性和可扩展性。

### 1. 模块目标

*   作为所有浏览器扩展配置的单一真实来源。
*   支持为同一扩展生成针对不同浏览器和发布渠道的定制化配置。
*   自动化处理配置的继承、合并、国际化和文件生成。
*   在配置处理过程中提供严格的验证机制，确保输出的配置有效且符合规范。
*   提供清晰的接口和辅助工具，提升开发者体验。

### 2. 核心需求概述

该模块需要满足以下核心需求：

*   **配置定义**：允许开发者以结构化方式定义扩展的全局配置和针对特定渠道的变体配置。
*   **配置初步处理 (`defineExtensionConfig`)**：对原始配置进行初步的扁平化和核心元数据确定。
*   **配置深度处理**：基于初步处理的数据，进行更复杂的配置合并、国际化处理和权限分离等。
*   **国际化处理**：支持多语言文案的管理，包括语言包的发现、合并、条件化解析和多种格式的输出。
*   **文件生成**：将处理后的配置和国际化数据以标准化格式写入文件系统，供后续构建流程使用。
*   **验证与错误处理**：在配置处理的各个阶段进行严格的数据验证，并提供明确的错误和警告信息。
*   **辅助功能**：提供查询、清理配置等辅助能力。

### 3. 详细需求

#### 3.1. 配置源与定义 (`extension.config.ts`)

`extension.config.ts` 是每个扩展的唯一配置入口，开发者通过 `defineExtensionConfig` 函数定义配置。

**顶层配置字段：**

*   `name` (string, **必填**): 插件的唯一标识名，通常与 `packages/extensions/` 下的目录名一致。
*   `version` (string, **必填**): 插件的版本号，所有插件渠道类型共享此版本。
*   `manifestVersion` (number, 可选): 默认的 Manifest 版本 (2 或 3)。如果未指定，系统将使用默认值（通常为 3）。此值可被渠道包配置覆盖。
*   `defaultLocale` (string, 可选): 默认的语言包代码（例如 'en'）。如果未指定，系统将使用默认值（通常为 'en'）。此值可被渠道包配置覆盖。
*   `manifest` (object, 可选): 全局共享的 `manifest.json` 基础配置。此对象将与渠道包中的 `manifest` 配置进行深度合并。
    *   `name` (string, 可选): 插件名称。
    *   `description` (string, 可选): 插件描述。
    *   `icons` (object, 可选): 插件图标配置。
    *   `permissions` (string[], 可选): 插件所需的常规权限列表。
    *   `host_permissions` (string[], 可选): 插件所需的主机权限列表。
    *   `optional_permissions` (string[], 可选): 插件所需的常规可选权限列表。
    *   `optional_host_permissions` (string[], 可选): 插件所需的主机可选权限列表。
    *   其他标准 Manifest 字段。
*   `i18n` (object, 可选): 全局的国际化处理配置。
    *   `locales` (string[], 可选): **注意：此字段在代码中会被忽略，语言列表通过扫描目录自动获取。** 如果手动设置，系统会发出警告。
    *   `includeKeys` (string[], 可选): 用于过滤语言包 key 的正则表达式数组。只有匹配这些模式的 key 才会被包含。
    *   `excludeKeys` (string[], 可选): 用于过滤语言包 key 的正则表达式数组。匹配这些模式的 key 将被排除。
    *   `chromeOnlyLocales` (string[], 可选): 仅生成 Chrome 语言包格式的语言代码列表（例如 `['en_US', 'pt_BR']`）。
    *   `chromeOnlyKeys` (string[], 可选): 在 Chrome 语言包中只包含这些 key 的正则表达式数组（例如 `['EXTENSION_NAME', 'EXTENSION_DESCRIPTION']`）。
*   `variants` (array, **必填**): 定义所有插件渠道类型的数组。每个对象定义一个具体的插件渠道类型。
*   `measurementId` (string, 可选): Google Analytics 测量 ID。

**`variants` 数组中的每个对象结构：**

*   `variantId` (string, **必填**): 插件渠道的唯一 ID，通常由后端提供。
*   `variantName` (string, **必填**): 插件渠道的名称，通常由后端提供。
*   `variantType` (string, **必填**): 插件渠道类型（例如 'master', 'tm', 'offline', 'dba', 'tmBeta'）。
*   `webstore` (string, **必填**): 目标浏览器商店（例如 'chrome', 'firefox', 'browser360', 'safari', 'adspower', 'opera', 'edge'）。
*   `webstoreId` (string, 可选): 浏览器商店中的扩展 ID。
*   `webstoreUrl` (string, 可选): 浏览器商店中扩展的 URL。
*   `measurementId` (string, 可选): 特定于此渠道包的 Google Analytics 测量 ID，将覆盖顶层配置。
*   `manifestVersion` (number, 可选): 特定于此渠道包的 Manifest 版本，将覆盖顶层配置。
*   `defaultLocale` (string, 可选): 特定于此渠道包的默认语言，将覆盖顶层配置。
*   `manifest` (object, 可选): 特定于此插件渠道类型的 `manifest.json` 配置，将与顶层 `manifest` 进行深度合并。
*   `i18n` (object, 可选): 特定于此插件渠道类型的国际化配置，将与顶层 `i18n` 进行合并。

#### 3.2. `defineExtensionConfig` 的需求层面作用

在插件配置管理模块中，`defineExtensionConfig` 函数在需求层面扮演着**原始配置的初步扁平化和核心元数据确定的角色**。它的核心需求是：

1.  **作为原始配置的标准化入口**：它要求开发者使用一个特定的函数来定义 `extension.config.ts` 文件中的所有插件配置。这确保了配置的结构化和标准化，并为后续的自动化处理奠定了基础。
2.  **执行基础的配置扁平化与合并**：它负责将 `extension.config.ts` 中定义的顶层配置（如 `name`, `version`, `manifest`, `i18n` 等）与 `variants` 数组中每个渠道包的配置进行**简单的、浅层的合并**。这意味着对于 `manifest` 和 `i18n` 这样的复杂对象，它只会将顶层和渠道包的对应字段进行浅层合并，而不会进行深度递归合并、权限分离或语言包内容的具体处理。
3.  **确定核心渠道元数据**：对于每个渠道包，它需要根据优先级规则（渠道包配置优先于顶层配置）确定并输出以下关键元数据：
    *   `manifestVersion`：最终确定的 Manifest 版本。
    *   `defaultLocale`：最终确定的默认语言。
    *   `measurementId`：最终确定的 Google Analytics 测量 ID。
    *   以及 `variantId`, `variantName`, `variantType`, `webstore`, `webstoreId`, `webstoreUrl` 等渠道包自身的基本信息。
4.  **生成唯一的渠道目标标识**：根据渠道包的 `webstore`、`variantType` 和确定的 `manifestVersion`，生成一个唯一的 `variantTarget` 字符串（例如 `chrome-mv3-master`），作为该渠道包的唯一标识符。
5.  **输出初步处理后的渠道配置集合**：它最终返回一个以 `variantTarget` 为键，包含每个渠道包初步扁平化后的配置对象集合。这个集合的结构严格遵循您提供的示例：
    ```json
    {
        name: string; // 扩展名称
        version: string; // 扩展版本
        variants: {
            'chrome-mv3-master': {
                name: string; // 扩展名称
                version: string; // 扩展版本
                manifestVersion: ManifestVersionType; // 最终确定的 manifest 版本
                defaultLocale: string; // 最终确定的默认语言
                variantID: string; // 渠道包ID，例如 '1111'
                variantName: string; // 渠道包名称，例如 'master'
                variantType: string; // 渠道包类型，例如 'master'
                variantChannel: string; // 渠道包类型标识，等同于 webstore。如果是 'offline' 则为 '{webstore}_offline'
                variantTarget: 'chrome-mv3-master'; // 完整的渠道包标识，例如 'chrome-mv3-master'，用于文件名和路径
                webstoreCN: string; // 渠道包的浏览器标识，例如 'e-c' 或 'e-f'，没有就留空
                webstore: string; // 目标浏览器，例如 'chrome' 或 'firefox'
                webstoreId?: string; // 浏览器商店 ID，例如 'fgfdgrifnekkkcbapdfandpixbdfhh'
                webstoreUrl?: string; // 浏览器商店 URL，留空就用 webstoreId 生成
                measurementId?: string; // GA 测量 ID，例如 'G-PJYG13CSDE'

                i18n: {}; // 和顶层的 i18n 合并后的配置
                manifest: {}; // 和顶层的 manifest 合并后的配置。manifest_version 和 default_locale 将会被移除，后续再自动填充
            },
            'firefox-mv3-master': {},
            // ... 其他渠道包
        }
    }
    ```

#### 3.3. 国际化处理

*   **语言包发现**：系统将自动扫描 `packages/shared/locales/` 和 `packages/extensions/{extensionName}/locales/` 目录来获取所有可用的语言列表。
*   **语言包合并**：对于每个语言，共享语言包和扩展专属语言包中的文案将进行合并，扩展专属文案具有更高优先级。
*   **条件化文案解析**：支持文案键名中包含渠道特定标识（例如 `KEY@webstore-mv-variant`、`KEY@webstore-variant`、`KEY@webstore`）。系统将根据当前渠道包的 `variantTarget` 智能匹配最具体的文案。
*   **文案过滤**：根据 `i18n.includeKeys` 和 `i18n.excludeKeys` 定义的正则表达式模式，对文案键进行过滤。
*   **多格式输出**：
    *   **Vue I18n 格式**：将 `$placeholder$` 格式的占位符转换为 `{placeholder}` 形式，用于前端界面。
    *   **Chrome 扩展格式**：将 `$placeholder$` 格式的占位符转换为 `$1`、`$2` 形式，并自动生成 `placeholders` 定义，用于 `_locales/messages.json`。
        *   **`chromeOnlyLocales` 的作用**：通过在 `extension.config.ts` 的 `i18n` 配置中设置 `chromeOnlyLocales` 字段，可以指定某些语言（例如 `['en_US', 'pt_BR']`）仅生成 Chrome 扩展格式的语言包。这意味着这些语言将不会生成 Vue I18n 格式的语言包，适用于仅需在 Chrome 环境下使用的特定语言资源。
        *   **`chromeOnlyKeys` 的作用**：通过在 `extension.config.ts` 的 `i18n` 配置中设置 `chromeOnlyKeys` 字段，可以指定一个正则表达式数组（例如 `['EXTENSION_NAME', 'EXTENSION_DESCRIPTION']`）。只有匹配这些模式的国际化键才会被包含在 Chrome 扩展格式的语言包中。这允许开发者精确控制哪些键仅用于 Chrome Manifest 或 Chrome API 相关的国际化文本，从而优化语言包大小和加载性能。

#### 3.4. manifest 配置处理机制

* **优先级解析**：manifest 相关字段（如 manifestVersion、defaultLocale）会根据如下优先级自动解析，渠道包配置优先于顶层配置：
    - `variant.manifest.manifest_version` > `variant.manifestVersion` > `ext.manifest.manifest_version` > `ext.manifestVersion` > 默认 3
    - `variant.manifest.default_locale` > `variant.defaultLocale` > `ext.manifest.default_locale` > `ext.defaultLocale` > 默认 'en'
* **深度合并**：manifest 对象会进行深度合并，渠道包的 manifest 字段会覆盖顶层 manifest。对于数组字段（如 permissions），会自动去重合并。
* **自动填充字段**：
    - 自动填充 `version`、`manifest_version`、`default_locale`。
    - 自动生成 `homepage_url`（带参数）。
    - 根据 webstore 自动生成 `update_url`（如有需要）。
* **权限分离**：
    - 自动将 permissions 拆分为 `permissions` 和 `host_permissions`。
    - 可选权限同理，拆分为 `optional_permissions` 和 `optional_host_permissions`。
* **最终产物**：
    - 处理后的 manifest 会写入 `.variants/{variantTarget}/extension.config.json`，并在生产模式下单独备份到 `.manifests/manifest.{variantTarget}.json`。
    - manifest 字段会包含所有合并、填充和权限分离后的最终结果，供后续构建和打包使用。

#### 3.5. 文件生成与最终产物

系统将为每个处理后的渠道包生成一套独立的配置文件和语言包文件，输出到扩展内部的 `.variants/{variantTarget}/` 目录。

**最终产物 (`.variants/{variantTarget}/extension.config.json`) 结构：**

这是一个扁平化的、已完全解析的配置对象，包含了所有继承、合并和 i18n 解析后的最终结果。

*   `name` (string): 扩展名称。
*   `version` (string): 扩展版本。
*   `manifestVersion` (number): 最终确定的 Manifest 版本。
*   `defaultLocale` (string): 最终确定的默认语言。
*   `variantID` (string): 渠道包 ID。
*   `variantName` (string): 渠道包名称。
*   `variantType` (string): 渠道包类型。
*   `variantChannel` (string): 渠道包类型标识，等同于 `webstore`。如果是 `offline` 类型，则为 `{webstore}_offline`。
*   `variantTarget` (string): 完整的渠道包标识（例如 'chrome-mv3-master'），用于文件名和路径。
*   `webstoreCN` (string): 渠道包的中文浏览器标识（例如 'e-c'）。
*   `webstore` (string): 目标浏览器。
*   `webstoreId` (string, 可选): 浏览器商店 ID。
*   `webstoreUrl` (string, 可选): 浏览器商店 URL。
*   `measurementId` (string, 可选): GA 测量 ID。
*   `i18n` (object): 合并后的国际化配置元数据。
    *   `locales` (string[]): 支持的语言列表（通过扫描目录自动获取）。
    *   `includeKeys` (string[]): 原始配置中的包含键模式。
    *   `excludeKeys` (string[]): 原始配置中的排除键模式。
    *   `chromeOnlyLocales` (string[]): 原始配置中的 Chrome 专用语言列表。
    *   `chromeOnlyKeys` (string[]): 原始配置中的 Chrome 专用键模式。
    *   `vueMessages` (object): **此字段不会写入 `extension.config.json`**，但会在内存中生成，并单独写入 `i18n.json`。
    *   `chromeMessages` (object): **此字段不会写入 `extension.config.json`**，但会在内存中生成，并单独写入 `_locales/messages.json`。
*   `manifest` (object): 完整的 Manifest 配置，所有字段都已合并、填充和权限分离。
    *   `manifest_version` (number): 最终确定的 Manifest 版本。
    *   `default_locale` (string, 可选): 最终确定的默认语言。
    *   `version` (string): 插件版本。
    *   `homepage_url` (string): 自动生成的主页 URL。
    *   `update_url` (string, 可选): 自动生成的更新 URL。
    *   `permissions` (string[]): 常规权限列表。
    *   `host_permissions` (string[]): 主机权限列表。
    *   `optional_permissions` (string[], 可选): 可选常规权限列表。
    *   `optional_host_permissions` (string[], 可选): 可选主机权限列表。
    *   其他标准 Manifest 字段。
*   `paths` (object): 处理后的所有项目相关路径信息。
    *   `workspace` (string): 工作区根目录。
    *   `packages` (string): `packages` 目录路径。
    *   `extensions` (string): `packages/extensions` 目录路径。
    *   `extensionRoot` (string): 特定扩展的根目录。
    *   `extensionRawConfig` (string): 原始 `extension.config.ts` 文件路径。
    *   `extensionRawLocales` (string): 原始 `locales` 目录路径。
    *   `extensionVariantsRoot` (string): 扩展内部 `.variants` 目录路径。
    *   `extensionVariantTargetRoot` (string): 特定渠道包的输出根目录（例如 `.variants/chrome-mv3-master/`）。
    *   `extensionVariantTargetJSON` (string): 最终 `extension.config.json` 文件路径。
    *   `extensionVariantTargetI18n` (string): 最终 `i18n.json` 文件路径。
    *   `extensionVariantTargetLocales` (string): 最终 `_locales` 目录路径。
    *   `extensionVariantTargetManifest` (string): Manifest 备份文件路径（例如 `.manifests/manifest.chrome-mv3-master.json`）。
    *   `extensionVariantTargetOutput` (string): 最终构建产物输出目录（由构建系统决定）。
    *   `extensionVariantTargetManifest` (string): Manifest 备份文件路径（例如 `.manifests/manifest.chrome-mv3-master.json`，**仅生产模式下生成**）。

**其他生成的国际化文件：**

*   `.variants/{variantTarget}/i18n.json`：包含 Vue I18n 格式的语言包内容（`{ "messages": { "locale_code": { "key": "value" } } }`）。
*   `.variants/{variantTarget}/public/_locales/{locale}/messages.json`：包含 Chrome 扩展格式的语言包内容（`{ "key": { "message": "value", "placeholders": {} } }`）。

#### 3.6. 验证与错误处理

*   **配置验证**：在配置处理的早期阶段，对原始配置和合并后的配置进行严格验证，包括：
    *   必填字段检查（例如 `name`、`version`、`variantId`、`webstore`、`variantType`）。
    *   枚举值有效性检查（例如 `webstore`、`variantType`、`manifestVersion` 是否在支持列表中）。
    *   `variantTarget` 格式和唯一性检查，避免重复的渠道目标。
*   **错误与警告机制**：
    *   对于严重错误（如必填字段缺失、无效值），应终止当前渠道包的处理并抛出明确的错误信息。
    *   对于非致命问题（如手动设置的 `i18n.locales` 被忽略），应发出警告信息。
*   **日志记录**：在处理过程中应记录详细的日志，包括信息、警告和错误，以便于调试和问题排查。

#### 3.7. 辅助功能与工具

*   **配置加载接口**：提供一个公共接口，允许外部模块加载和获取指定扩展的已处理配置，并可选择性地触发文件生成。
*   **配置模板**：提供一个基础配置模板，方便开发者快速创建新的 `extension.config.ts` 文件。
*   **列出扩展名称**：扫描 `packages/extensions` 目录，查找包含 `extension.config.ts` 文件的子目录，并返回这些目录的名称。
*   **列出扩展渠道包信息**：根据指定的扩展名称（如果未指定，则列出所有扩展），加载其 `extension.config.ts` 文件，并返回所有对应的渠道包信息。
*   **清理配置缓存**：清理指定扩展（如果未指定，则清理所有扩展）生成的 `.variants` 缓存目录。

### 4. 模块间关系（需求层面）

```mermaid
graph TD
    A[开发者定义 extension.config.ts] --> B(插件配置管理模块);
    B --> C[配置处理 (合并, 填充, 权限分离)];
    B --> D[国际化处理 (扫描, 合并, 条件化, 格式化)];
    C & D --> E[文件生成];
    E --> F[标准化配置文件和语言包 (.variants/)];
    F --> G[构建系统 (消费配置)];
    B --> H[验证与错误报告];
    B --> I[辅助工具 (列表, 清理)];
