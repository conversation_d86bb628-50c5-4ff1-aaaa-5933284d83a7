/**
 * @fileoverview 插件配置管理模块的核心类型定义和常量
 * @description 定义所有配置相关的 TypeScript 类型、枚举和常量映射
 */

import type { Browser } from 'wxt/browser';

// #region --- 基础枚举与字面量类型 ---

/**
 * @description 支持的浏览器商店类型
 */
export type WebstoreType =
  | 'chrome'
  | 'firefox'
  | 'browser360'
  | 'safari'
  | 'adspower'
  | 'opera'
  | 'edge';

/**
 * @description 浏览器商店的中文标识
 */
export type WebstoreCNType =
  | 'e-c'
  | 'e-f'
  | 'e-o'
  | 'e-360'
  | 'e-s'
  | 'e-ads'
  | 'e-edge'
  | WebstoreType;

/**
 * @description 插件变体类型
 */
export type VariantType = 'master' | 'tm' | 'tmBeta' | 'dba' | 'offline';

/**
 * @description 插件渠道类型标识
 */
export type VariantChannel = WebstoreType | `${WebstoreType}_offline`;

/**
 * @description Manifest 版本类型
 */
export type ManifestVersionType = 2 | 3;

// #endregion

// #region --- 原始配置相关类型 (extension.config.ts) ---

/**
 * @description i18n 配置
 */
export interface I18nConfig {
  /** 语言列表（会被自动扫描覆盖，手动设置会发出警告） */
  locales?: string[];
  /** 包含键模式的正则表达式数组 */
  includeKeys?: string[];
  /** 排除键模式的正则表达式数组 */
  excludeKeys?: string[];
  /** 仅生成 Chrome 语言包格式的语言代码列表 */
  chromeOnlyLocales?: string[];
  /** 在 Chrome 语言包中只包含这些 key 的正则表达式数组 */
  chromeOnlyKeys?: string[];
}

/**
 * @description Manifest.json 的基础配置类型
 */
export type BaseManifestConfig = Omit<Browser.runtime.Manifest, 'manifest_version'> & {
  manifest_version: ManifestVersionType;
  default_locale?: string;
};

/**
 * @description 用户在 extension.config.ts 中提供的 Manifest 配置
 */
export type ManifestConfig = Partial<BaseManifestConfig>;

/**
 * @description 单个渠道包（变体）的配置
 */
export interface VariantConfig {
  /** 插件渠道的唯一 ID */
  variantId: string;
  /** 插件渠道的名称 */
  variantName: string;
  /** 插件渠道类型 */
  variantType: VariantType;
  /** 目标浏览器商店 */
  webstore: WebstoreType;
  /** 浏览器商店中的扩展 ID */
  webstoreId?: string;
  /** 浏览器商店中扩展的 URL */
  webstoreUrl?: string;
  /** 特定于此渠道包的 Google Analytics 测量 ID */
  measurementId?: string;
  /** 特定于此渠道包的 Manifest 版本 */
  manifestVersion?: ManifestVersionType;
  /** 特定于此渠道包的默认语言 */
  defaultLocale?: string;
  /** 特定于此插件渠道类型的 manifest.json 配置 */
  manifest?: Partial<BaseManifestConfig>;
  /** 特定于此插件渠道类型的国际化配置 */
  i18n?: Partial<I18nConfig>;
}

/**
 * @description 原始的扩展配置 (来自 extension.config.ts)
 */
export interface ExtensionConfig {
  /** 插件的唯一标识名 */
  name: string;
  /** 插件的版本号 */
  version: string;
  /** 默认的 Manifest 版本 */
  manifestVersion?: ManifestVersionType;
  /** 默认的语言包代码 */
  defaultLocale?: string;
  /** 全局的国际化处理配置 */
  i18n?: I18nConfig;
  /** 全局共享的 manifest.json 基础配置 */
  manifest?: ManifestConfig;
  /** 定义所有插件渠道类型的数组 */
  variants: VariantConfig[];
  /** Google Analytics 测量 ID */
  measurementId?: string;
}

// #endregion

// #region --- 处理后的配置类型 ---

/**
 * @description 经过处理后的所有项目相关路径
 */
export interface ProcessedPaths {
  // 静态路径
  workspace: string;
  packages: string;
  extensions: string;
  shared: string;
  sharedLocales: string;
  dist: string;
  release: string;
  scripts: string;
  changelog: string;

  // 扩展相关路径
  extensionRoot: string;
  extensionRawConfig: string;
  extensionRawLocales: string;
  extensionVariantsRoot: string;
  extensionVariantTargetRoot: string;
  extensionVariantTargetJSON: string;
  extensionVariantTargetI18n: string;
  extensionVariantTargetLocales: string;
  extensionManifestsRoot: string;
  extensionVariantTargetManifest: string;
  extensionVariantTargetOutput: string;
}

/**
 * @description 经过 defineExtensionConfig 初步处理后的基础变体配置，包含扩展名称、版本、变体配置
 * 这是第一阶段的处理结果，只包含基本的配置合并和元数据确定
 */
export interface ProcessedBaseVariantConfig {
  /** 扩展名称 */
  name: string;
  /** 扩展版本 */
  version: string;
  /** 最终确定的 manifest 版本 */
  manifestVersion: ManifestVersionType;
  /** 最终确定的默认语言 */
  defaultLocale: string;
  /** 渠道包ID */
  variantID: string;
  /** 渠道包名称 */
  variantName: string;
  /** 渠道包类型 */
  variantType: VariantType;
  /** 渠道包类型标识，等同于 webstore。如果是 'offline' 则为 '{webstore}_offline' */
  variantChannel: VariantChannel;
  /** 完整的渠道包标识，用于文件名和路径 */
  variantTarget: string;
  /** 渠道包的浏览器标识 */
  webstoreCN: WebstoreCNType;
  /** 目标浏览器 */
  webstore: WebstoreType;
  /** 浏览器商店 ID */
  webstoreId?: string;
  /** 浏览器商店 URL */
  webstoreUrl?: string;
  /** GA 测量 ID */
  measurementId?: string;
  /** 合并后的 i18n 配置元数据（浅层合并） */
  i18n: I18nConfig;
  /** 合并后的 manifest 配置（浅层合并） */
  manifest: ManifestConfig;
}

/**
 * @description 最终处理完成的、用于输出的单个变体配置
 * 包含完整的路径信息和深度处理后的配置
 */
export interface ProcessedVariantConfig extends ProcessedBaseVariantConfig {
  /** 合并后的 i18n 配置元数据 */
  i18n: I18nConfig;
  /** 合并后的 manifest 配置 */
  manifest: BaseManifestConfig;
  /** 处理后的路径信息，包含所有相关路径 */
  paths: ProcessedPaths;
}

// #endregion

// #region --- 验证相关类型 ---

/**
 * @description 验证结果
 */
export interface ValidationResult {
  /** 是否验证通过 */
  isValid: boolean;
  /** 错误信息列表 */
  errors: string[];
  /** 警告信息列表 */
  warnings: string[];
}

/**
 * @description 插件渠道包的结构化信息
 */
export interface VariantInfo {
  webstore: WebstoreType;
  mv: string;
  variant: VariantType;
  target: string;
  'webstore-variant': string;
}

// #endregion

// #region --- 常量定义 ---

/**
 * @description 支持的浏览器商店
 */
export const SUPPORT_WEBSTORE = {
  chrome: 'chrome',
  firefox: 'firefox',
  browser360: 'browser360',
  safari: 'safari',
  adspower: 'adspower',
  opera: 'opera',
  edge: 'edge',
} as const;

export const SUPPORT_WEBSTORES: WebstoreType[] = Object.values(SUPPORT_WEBSTORE) as WebstoreType[];

/**
 * @description 浏览器商店的中文标识映射
 * 参考文档：https://easydoc.net/doc/91904722/fvS6AHYM/9TYy2p86
 */
export const WEBSTORE_CN: Record<WebstoreType, string> = {
  chrome: 'e-c',
  firefox: 'e-f',
  opera: 'e-o',
  browser360: 'e-360',
  safari: 'e-s',
  adspower: 'e-ads',
  edge: 'e-edge',
};

/**
 * @description 各应用商店的扩展自动更新地址
 * 参考：https://learn.microsoft.com/zh-cn/microsoft-edge/extensions-chromium/publish/auto-update
 */
export const WEBSTORES_UPDATE_URL: Record<WebstoreType | 'aliprice', string> = {
  chrome: 'https://clients2.google.com/service/update2/crx',
  opera: 'https://clients2.google.com/service/update2/crx',
  browser360: 'http://upext.chrome.360.cn/intf.php?method=ExtUpdate.query',
  firefox: '',
  safari: '',
  adspower: '',
  edge: '',
  aliprice: 'https://www.aliprice.com/extension_page/{extensionName}/updates.json',
};

/**
 * @description 各应用商店扩展的基础 URL
 */
export const EXTENSION_BASE_URL: Record<WebstoreType, string> = {
  chrome: 'chrome-extension://__MSG_@@extension_id__',
  opera: 'chrome-extension://__MSG_@@extension_id__',
  browser360: 'chrome-extension://__MSG_@@extension_id__',
  firefox: 'moz-extension://__MSG_@@extension_id__',
  safari: 'chrome-extension://__MSG_@@extension_id__',
  adspower: 'chrome-extension://__MSG_@@extension_id__',
  edge: 'chrome-extension://__MSG_@@extension_id__',
};

/**
 * @description 支持的 Manifest 版本
 */
export const SUPPORT_MANIFEST_VERSIONS: ManifestVersionType[] = [2, 3];

/**
 * @description 支持的变体类型
 */
export const SUPPORT_VARIANT = {
  master: 'master',
  tm: 'tm',
  tmBeta: 'tmBeta',
  dba: 'dba',
  offline: 'offline',
} as const;

export const SUPPORT_VARIANTS: VariantType[] = Object.values(SUPPORT_VARIANT) as VariantType[];

/**
 * @description 默认的 Chrome 专用语言包
 */
export const DEFAULT_CHROME_ONLY_LOCALES = ['en_US', 'en_GB', 'pt_BR', 'es_419'];

/**
 * @description 默认的 Chrome 专用键值模式
 */
export const DEFAULT_CHROME_ONLY_KEYS = [
  'EXTENSION_NAME',
  'EXTENSION_DESCRIPTION',
  '^context_menu_.*',
];

// #endregion
