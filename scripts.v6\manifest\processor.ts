/**
 * @fileoverview manifest 模块的核心处理器
 * @description 实现 Manifest 配置的深度合并、权限分离、自动填充等核心功能
 */

import { createLogger } from '../shared/logger.js';
import type {
  ManifestProcessOptions,
  ManifestProcessResult,
  BaseManifestConfig,
  ManifestConfig,
  WebstoreType,
  PermissionClassification,
  PermissionSeparationConfig,
  PermissionSeparationResult,
  UrlGenerationOptions,
} from './types.js';

const logger = createLogger('ManifestProcessor');

// #region --- 常量定义 ---

/**
 * @description 各应用商店的扩展自动更新地址
 */
const WEBSTORES_UPDATE_URL: Record<WebstoreType | 'aliprice', string> = {
  chrome: 'https://clients2.google.com/service/update2/crx',
  opera: 'https://clients2.google.com/service/update2/crx',
  browser360: 'http://upext.chrome.360.cn/intf.php?method=ExtUpdate.query',
  firefox: '',
  safari: '',
  adspower: '',
  edge: '',
  aliprice: 'https://www.aliprice.com/extension_page/{extensionName}/updates.json',
};

/**
 * @description 主机权限模式
 */
const HOST_PERMISSION_PATTERNS = [
  /^https?:\/\/\*\//,
  /^https?:\/\/[^\/]+\//,
  /^https?:\/\/\*\.[^\/]+\//,
  /^\*:\/\/\*\//,
  /^\*:\/\/[^\/]+\//,
  /^\*:\/\/\*\.[^\/]+\//,
  /^file:\/\/\//,
  /^<all_urls>$/,
];

// #endregion

// #region --- 深度合并 ---

/**
 * @description 检查值是否为普通对象
 */
function isPlainObject(value: any): value is Record<string, any> {
  return value !== null && typeof value === 'object' && value.constructor === Object;
}

/**
 * @description 深度合并两个对象
 * @param target 目标对象
 * @param source 源对象
 * @returns 合并后的对象
 */
function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      const sourceValue = source[key];
      const targetValue = result[key];

      if (Array.isArray(sourceValue)) {
        // 数组类型：合并并去重
        const targetArray = Array.isArray(targetValue) ? targetValue : [];
        result[key] = [...new Set([...targetArray, ...sourceValue])] as any;
      } else if (isPlainObject(sourceValue) && isPlainObject(targetValue)) {
        // 对象类型：递归深度合并
        result[key] = deepMerge(targetValue, sourceValue);
      } else {
        // 其他类型：直接覆盖
        result[key] = sourceValue;
      }
    }
  }

  return result;
}

/**
 * @description 合并顶层和变体的 Manifest 配置
 * @param topLevel 顶层配置
 * @param variant 变体配置
 * @returns 合并后的配置
 */
function mergeManifestConfigs(topLevel?: ManifestConfig, variant?: ManifestConfig): ManifestConfig {
  if (!topLevel && !variant) {
    return {};
  }
  if (!topLevel) {
    return { ...variant };
  }
  if (!variant) {
    return { ...topLevel };
  }

  return deepMerge(topLevel, variant);
}

// #endregion

// #region --- 权限分离 ---

/**
 * @description 判断权限是否为主机权限
 * @param permission 权限字符串
 * @returns 是否为主机权限
 */
function isHostPermission(permission: string): boolean {
  return HOST_PERMISSION_PATTERNS.some((pattern) => pattern.test(permission));
}

/**
 * @description 分类权限为常规权限和主机权限
 * @param permissions 权限列表
 * @returns 分类结果
 */
function classifyPermissions(permissions: string[]): PermissionClassification {
  const regularPermissions: string[] = [];
  const hostPermissions: string[] = [];

  permissions.forEach((permission) => {
    if (isHostPermission(permission)) {
      hostPermissions.push(permission);
    } else {
      regularPermissions.push(permission);
    }
  });

  return { regularPermissions, hostPermissions };
}

/**
 * @description 分离权限配置
 * @param config 权限配置
 * @returns 分离结果
 */
function separatePermissions(config: PermissionSeparationConfig): PermissionSeparationResult {
  // 处理常规权限
  const allPermissions = [...(config.permissions || []), ...(config.hostPermissions || [])];
  const { regularPermissions, hostPermissions } = classifyPermissions(allPermissions);

  // 处理可选权限
  const allOptionalPermissions = [
    ...(config.optionalPermissions || []),
    ...(config.optionalHostPermissions || []),
  ];
  const {
    regularPermissions: optionalRegularPermissions,
    hostPermissions: optionalHostPermissions,
  } = classifyPermissions(allOptionalPermissions);

  return {
    permissions: [...new Set(regularPermissions)],
    hostPermissions: [...new Set(hostPermissions)],
    optionalPermissions: [...new Set(optionalRegularPermissions)],
    optionalHostPermissions: [...new Set(optionalHostPermissions)],
  };
}

// #endregion

// #region --- URL 生成 ---

/**
 * @description 生成主页 URL
 * @param options URL 生成选项
 * @returns 主页 URL
 */
function generateHomepageUrl(options: UrlGenerationOptions): string {
  const { extensionName, measurementId } = options;
  let url = `https://www.aliprice.com/extension_page/${extensionName}/`;

  if (measurementId) {
    url += `?utm_source=extension&utm_medium=homepage&utm_campaign=${measurementId}`;
  }

  return url;
}

/**
 * @description 生成更新 URL
 * @param webstore 浏览器商店类型
 * @param extensionName 扩展名称
 * @returns 更新 URL
 */
function generateUpdateUrl(webstore: WebstoreType, extensionName: string): string {
  const baseUrl = WEBSTORES_UPDATE_URL[webstore];

  if (!baseUrl) {
    return '';
  }

  if (baseUrl.includes('{extensionName}')) {
    return baseUrl.replace('{extensionName}', extensionName);
  }

  return baseUrl;
}

/**
 * @description 生成浏览器商店 URL
 * @param webstore 浏览器商店类型
 * @param webstoreId 商店 ID
 * @param webstoreUrl 自定义 URL
 * @returns 商店 URL
 */
function generateWebstoreUrl(
  webstore: WebstoreType,
  webstoreId?: string,
  webstoreUrl?: string,
): string {
  if (webstoreUrl) {
    return webstoreUrl;
  }

  if (!webstoreId) {
    return '';
  }

  const storeUrls: Record<WebstoreType, string> = {
    chrome: `https://chrome.google.com/webstore/detail/${webstoreId}`,
    firefox: `https://addons.mozilla.org/firefox/addon/${webstoreId}`,
    opera: `https://addons.opera.com/extensions/details/${webstoreId}`,
    edge: `https://microsoftedge.microsoft.com/addons/detail/${webstoreId}`,
    safari: `https://apps.apple.com/app/${webstoreId}`,
    browser360: `https://ext.chrome.360.cn/webstore/detail/${webstoreId}`,
    adspower: '', // AdsPower 没有公开商店
  };

  return storeUrls[webstore] || '';
}

// #endregion

// #region --- 主处理函数 ---

/**
 * @description 处理 Manifest 配置，执行深度合并、权限分离、自动填充等操作
 * @param options 处理选项
 * @returns 处理结果
 */
export function processManifest(options: ManifestProcessOptions): ManifestProcessResult {
  const {
    extensionName,
    version,
    variantTarget,
    manifestVersion,
    defaultLocale,
    webstore,
    webstoreId,
    webstoreUrl,
    measurementId,
    topLevelManifest,
    variantManifest,
  } = options;

  logger.info(`开始处理 Manifest 配置: ${extensionName} (${variantTarget})`);

  const warnings: string[] = [];
  const autoFilledFields: string[] = [];

  // 1. 深度合并配置
  const mergedConfig = mergeManifestConfigs(topLevelManifest, variantManifest);
  logger.verbose(`配置合并完成，包含 ${Object.keys(mergedConfig).length} 个字段`);

  // 2. 分离权限
  const permissionSeparation = separatePermissions({
    permissions: mergedConfig.permissions,
    hostPermissions: mergedConfig.host_permissions,
    optionalPermissions: mergedConfig.optional_permissions,
    optionalHostPermissions: mergedConfig.optional_host_permissions,
  });

  logger.verbose(
    `权限分离完成: 常规权限 ${permissionSeparation.permissions.length} 个，主机权限 ${permissionSeparation.hostPermissions.length} 个`,
  );

  // 3. 构建基础 Manifest
  const processedManifest: BaseManifestConfig = {
    ...mergedConfig,
    manifest_version: manifestVersion,
    version,
    permissions: permissionSeparation.permissions,
    host_permissions: permissionSeparation.hostPermissions,
  };

  // 4. 自动填充字段

  // 填充 default_locale
  if (defaultLocale && defaultLocale !== 'en') {
    processedManifest.default_locale = defaultLocale;
    autoFilledFields.push('default_locale');
  }

  // 填充 homepage_url
  if (!processedManifest.homepage_url) {
    processedManifest.homepage_url = generateHomepageUrl({
      extensionName,
      webstore,
      webstoreId,
      webstoreUrl,
      measurementId,
    });
    autoFilledFields.push('homepage_url');
  }

  // 填充 update_url（如果需要）
  const updateUrl = generateUpdateUrl(webstore, extensionName);
  if (updateUrl && !processedManifest.update_url) {
    processedManifest.update_url = updateUrl;
    autoFilledFields.push('update_url');
  }

  // 处理可选权限
  if (permissionSeparation.optionalPermissions.length > 0) {
    processedManifest.optional_permissions = permissionSeparation.optionalPermissions;
  }
  if (permissionSeparation.optionalHostPermissions.length > 0) {
    processedManifest.optional_host_permissions = permissionSeparation.optionalHostPermissions;
  }

  // 5. 验证和警告

  // 检查必要字段
  if (!processedManifest.name) {
    warnings.push('Manifest 中缺少 name 字段');
  }
  if (!processedManifest.description) {
    warnings.push('Manifest 中缺少 description 字段');
  }

  // 检查 Manifest V3 特定要求
  if (manifestVersion === 3) {
    if (processedManifest.background && 'scripts' in processedManifest.background) {
      warnings.push('Manifest V3 不支持 background.scripts，请使用 background.service_worker');
    }
    if (
      processedManifest.web_accessible_resources &&
      Array.isArray(processedManifest.web_accessible_resources)
    ) {
      warnings.push('Manifest V3 的 web_accessible_resources 应该是对象数组格式');
    }
  }

  // 检查 Firefox 特定要求
  if (webstore === 'firefox') {
    if (!processedManifest.browser_specific_settings?.gecko?.id) {
      warnings.push('Firefox 扩展需要在 browser_specific_settings.gecko.id 中指定扩展 ID');
    }
  }

  // 输出警告
  warnings.forEach((warning) => {
    logger.warn(warning);
  });

  logger.success(
    `Manifest 处理完成: 自动填充 ${autoFilledFields.length} 个字段，${warnings.length} 个警告`,
  );

  return {
    processedManifest,
    permissionsInfo: {
      permissions: permissionSeparation.permissions,
      hostPermissions: permissionSeparation.hostPermissions,
      optionalPermissions: permissionSeparation.optionalPermissions,
      optionalHostPermissions: permissionSeparation.optionalHostPermissions,
    },
    autoFilledFields,
    warnings,
  };
}

// #endregion
