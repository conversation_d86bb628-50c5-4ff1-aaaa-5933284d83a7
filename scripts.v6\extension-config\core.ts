/**
 * @fileoverview 插件配置管理模块的核心处理逻辑
 * @description 实现 defineExtensionConfig 函数、配置验证器、路径管理和配置扁平化合并
 */

import path from 'path';
import { createLogger } from '../shared/logger.js';
import type {
  ExtensionConfig,
  ProcessedVariantConfig,
  ProcessedPaths,
  ValidationResult,
  WebstoreType,
  VariantType,
  ManifestVersionType,
  VariantChannel,
  WebstoreCNType,
} from './types.js';
import {
  SUPPORT_WEBSTORES,
  SUPPORT_VARIANTS,
  SUPPORT_MANIFEST_VERSIONS,
  WEBSTORE_CN,
} from './types.js';

const logger = createLogger('ExtensionConfig');

// #region --- 路径管理 ---

/**
 * @description 获取工作区根目录
 */
function getWorkspaceRoot(): string {
  // 从当前文件位置推导工作区根目录
  return path.resolve(__dirname, '../../../');
}

/**
 * @description 生成指定扩展和变体的所有相关路径
 * @param extensionName 扩展名称
 * @param variantTarget 变体目标标识
 */
function generatePaths(extensionName: string, variantTarget: string): ProcessedPaths {
  const workspace = getWorkspaceRoot();
  const packages = path.join(workspace, 'packages');
  const extensions = path.join(packages, 'extensions');
  const shared = path.join(packages, 'shared');
  const extensionRoot = path.join(extensions, extensionName);

  return {
    // 静态路径
    workspace,
    packages,
    extensions,
    shared,
    sharedLocales: path.join(shared, 'locales'),
    dist: path.join(workspace, 'dist'),
    release: path.join(workspace, 'release'),
    scripts: path.join(workspace, 'scripts'),
    changelog: path.join(workspace, 'changelog.ts'),

    // 扩展相关路径
    extensionRoot,
    extensionRawConfig: path.join(extensionRoot, 'extension.config.ts'),
    extensionRawLocales: path.join(extensionRoot, 'locales'),
    extensionVariantsRoot: path.join(extensionRoot, '.variants'),
    extensionVariantTargetRoot: path.join(extensionRoot, '.variants', variantTarget),
    extensionVariantTargetJSON: path.join(
      extensionRoot,
      '.variants',
      variantTarget,
      'extension.config.json',
    ),
    extensionVariantTargetI18n: path.join(extensionRoot, '.variants', variantTarget, 'i18n.json'),
    extensionVariantTargetLocales: path.join(
      extensionRoot,
      '.variants',
      variantTarget,
      'public',
      '_locales',
    ),
    extensionManifestsRoot: path.join(extensionRoot, '.manifests'),
    extensionVariantTargetManifest: path.join(
      extensionRoot,
      '.manifests',
      `manifest.${variantTarget}.json`,
    ),
    extensionVariantTargetOutput: path.join(workspace, 'dist', extensionName, variantTarget),
  };
}

// #endregion

// #region --- 配置验证器 ---

/**
 * @description 验证原始扩展配置
 * @param config 原始扩展配置
 */
function validateExtensionConfig(config: ExtensionConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 验证必填字段
  if (!config.name) {
    errors.push('配置中缺少必填字段: name');
  }
  if (!config.version) {
    errors.push('配置中缺少必填字段: version');
  }
  if (!config.variants || config.variants.length === 0) {
    errors.push('配置中缺少必填字段: variants，或 variants 数组为空');
  }

  // 验证 manifestVersion
  if (config.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(config.manifestVersion)) {
    errors.push(
      `不支持的 manifestVersion: ${config.manifestVersion}，支持的版本: ${SUPPORT_MANIFEST_VERSIONS.join(', ')}`,
    );
  }

  // 验证 i18n.locales 手动设置警告
  if (config.i18n?.locales && config.i18n.locales.length > 0) {
    warnings.push('i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略');
  }

  // 验证变体配置
  const variantTargets = new Set<string>();
  config.variants.forEach((variant, index) => {
    const prefix = `variants[${index}]`;

    // 验证必填字段
    if (!variant.variantId) {
      errors.push(`${prefix} 缺少必填字段: variantId`);
    }
    if (!variant.variantName) {
      errors.push(`${prefix} 缺少必填字段: variantName`);
    }
    if (!variant.variantType) {
      errors.push(`${prefix} 缺少必填字段: variantType`);
    }
    if (!variant.webstore) {
      errors.push(`${prefix} 缺少必填字段: webstore`);
    }

    // 验证枚举值
    if (variant.variantType && !SUPPORT_VARIANTS.includes(variant.variantType)) {
      errors.push(
        `${prefix} 不支持的 variantType: ${variant.variantType}，支持的类型: ${SUPPORT_VARIANTS.join(', ')}`,
      );
    }
    if (variant.webstore && !SUPPORT_WEBSTORES.includes(variant.webstore)) {
      errors.push(
        `${prefix} 不支持的 webstore: ${variant.webstore}，支持的商店: ${SUPPORT_WEBSTORES.join(', ')}`,
      );
    }
    if (variant.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(variant.manifestVersion)) {
      errors.push(
        `${prefix} 不支持的 manifestVersion: ${variant.manifestVersion}，支持的版本: ${SUPPORT_MANIFEST_VERSIONS.join(', ')}`,
      );
    }

    // 生成并验证 variantTarget 唯一性
    if (variant.webstore && variant.variantType) {
      const manifestVersion = variant.manifestVersion || config.manifestVersion || 3;
      const variantTarget = `${variant.webstore}-mv${manifestVersion}-${variant.variantType}`;

      if (variantTargets.has(variantTarget)) {
        errors.push(
          `重复的 variantTarget: ${variantTarget}，请检查 webstore、manifestVersion 和 variantType 的组合`,
        );
      } else {
        variantTargets.add(variantTarget);
      }
    }

    // 验证 i18n.locales 手动设置警告
    if (variant.i18n?.locales && variant.i18n.locales.length > 0) {
      warnings.push(`${prefix} i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略`);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// #endregion

// #region --- 配置处理核心逻辑 ---

/**
 * @description 生成变体目标标识
 * @param webstore 浏览器商店
 * @param manifestVersion Manifest 版本
 * @param variantType 变体类型
 */
export function generateVariantTarget(
  webstore: WebstoreType,
  manifestVersion: ManifestVersionType,
  variantType: VariantType,
): string {
  return `${webstore}-mv${manifestVersion}-${variantType}`;
}

/**
 * @description 处理单个变体配置
 * @param config 原始扩展配置
 * @param variant 变体配置
 */
function processVariantConfig(config: ExtensionConfig, variant: any): ProcessedVariantConfig {
  // 确定核心元数据
  const manifestVersion =
    variant.manifest?.manifest_version ||
    variant.manifestVersion ||
    config.manifest?.manifest_version ||
    config.manifestVersion ||
    (3 as ManifestVersionType);
  const defaultLocale =
    variant.manifest?.default_locale ||
    variant.defaultLocale ||
    config.manifest?.default_locale ||
    config.defaultLocale ||
    'en';
  const measurementId = variant.measurementId || config.measurementId || '';

  // 生成标识符
  const variantTarget = generateVariantTarget(
    variant.webstore,
    manifestVersion,
    variant.variantType,
  );
  const variantChannel =
    variant.variantType === 'offline' ? `${variant.webstore}_offline` : variant.webstore;
  const webstoreCN = WEBSTORE_CN[variant.webstore as WebstoreType] as WebstoreCNType;

  // 合并配置
  const mergedI18n = { ...config.i18n, ...variant.i18n };
  const mergedManifest = {
    ...config.manifest,
    ...variant.manifest,
    manifest_version: manifestVersion,
    default_locale: defaultLocale,
  };

  return {
    name: config.name,
    version: config.version,
    manifestVersion,
    defaultLocale,
    variantID: variant.variantId,
    variantName: variant.variantName,
    variantType: variant.variantType,
    variantChannel,
    webstoreCN,
    webstore: variant.webstore,
    webstoreId: variant.webstoreId || '',
    webstoreUrl: variant.webstoreUrl || '',
    measurementId,
    variantTarget,
    i18n: mergedI18n,
    manifest: mergedManifest,
  };
}

/**
 * @description 在内存中处理 extension.config.ts，返回所有处理完成的变体配置
 * @param config 从 extension.config.ts 文件中导入的原始配置对象
 * @returns 一个以 variantTarget 为键，处理后的变体配置为值的对象
 */
export function defineExtensionConfig(
  config: ExtensionConfig,
): Record<string, ProcessedVariantConfig> {
  logger.verbose(`开始处理扩展配置: ${config.name}`);

  // 验证配置
  const validation = validateExtensionConfig(config);

  // 输出警告
  validation.warnings.forEach((warning) => {
    logger.warn(warning);
  });

  // 如果有错误，抛出异常
  if (!validation.isValid) {
    const errorMessage = `扩展配置验证失败:\n${validation.errors.join('\n')}`;
    logger.error(errorMessage);
    throw new Error(errorMessage);
  }

  // 处理所有变体配置
  const processedVariants: Record<string, ProcessedVariantConfig> = {};

  config.variants.forEach((variant) => {
    const processedVariant = processVariantConfig(config, variant);
    processedVariants[processedVariant.variantTarget] = processedVariant;
    logger.verbose(`处理变体配置: ${processedVariant.variantTarget}`);
  });

  logger.success(
    `扩展配置处理完成: ${config.name}，共 ${Object.keys(processedVariants).length} 个变体`,
  );

  return {
    name: config.name,
    version: config.version,
    variants: processedVariants,
  };
}

// #endregion
