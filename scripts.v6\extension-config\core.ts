/**
 * @fileoverview 插件配置管理模块的核心处理逻辑
 * @description 实现 defineExtensionConfig 函数、配置验证器和配置扁平化合并
 */

import { merge } from 'lodash-es';
import { createLogger } from '../shared/logger.js';
import type {
  ExtensionConfig,
  VariantConfig,
  ProcessedBaseVariantConfig,
  ValidationResult,
  WebstoreType,
  VariantType,
  ManifestVersionType,
  VariantChannel,
  WebstoreCNType,
  ProcessedVariantConfig,
} from './types.js';
import {
  SUPPORT_WEBSTORES,
  SUPPORT_VARIANTS,
  SUPPORT_MANIFEST_VERSIONS,
  WEBSTORE_CN,
} from './types.js';

const logger = createLogger('ExtensionConfig');

// #region --- 配置处理核心逻辑 ---

/**
 * @description 生成变体渠道标识
 * @param webstore 浏览器商店
 * @param variantType 变体类型
 */
function generateVariantChannel(webstore: WebstoreType, variantType: VariantType): VariantChannel {
  return variantType === 'offline' ? `${webstore}_offline` : webstore;
}

/**
 * @description 生成变体目标标识
 * @param webstore 浏览器商店
 * @param manifestVersion Manifest 版本
 * @param variantType 变体类型
 */
function generateVariantTarget(
  webstore: WebstoreType,
  manifestVersion: ManifestVersionType,
  variantType: VariantType,
): string {
  return `${webstore}-mv${manifestVersion}-${variantType}`;
}

function validateBaseVariantConfig(config: ProcessedBaseVariantConfig): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // --- 基本字段 ---
  if (!config.name?.trim()) errors.push('插件名字不能为空.');
  if (!config.version?.trim()) errors.push('插件版本不能为空.');
  if (!config.variantID?.trim()) errors.push('渠道包ID不能为空.');
  if (!config.variantName?.trim()) errors.push('渠道包名称不能为空.');
  if (!config.variantType?.trim()) errors.push('渠道包类型不能为空.');
  if (!config.variantTarget?.trim()) errors.push('完整的渠道包标识不能为空.');
  if (!config.webstore?.trim()) errors.push('目标浏览器不能为空.');

  // --- 特殊字段 ---
  if (config.webstore && !SUPPORT_WEBSTORES.includes(config.webstore)) {
    errors.push(`不支持目标浏览器: ${config.webstore}`);
  }
  if (config.variantType && !SUPPORT_VARIANTS.includes(config.variantType)) {
    errors.push(`不支持的渠道包类型: ${config.variantType}`);
  }
  if (config.manifestVersion && !SUPPORT_MANIFEST_VERSIONS.includes(config.manifestVersion)) {
    errors.push(`不支持的 Manifest 版本: ${config.manifestVersion}`);
  }
  if (!/[a-z]+-mv[23]-[a-z]+]/.test(config.variantTarget)) {
    errors.push(`无效的渠道包标识: ${config.variantTarget}`);
  }

  if (config.i18n?.locales && config.i18n.locales.length > 0) {
    warnings.push('i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略');
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * @description 简单处理单个变体配置（仅做基础合并，不生成路径）
 * @param config 原始扩展配置
 * @param variant 变体配置
 */
function processBaseVariantConfig(
  config: ExtensionConfig,
  variant: VariantConfig,
): ProcessedBaseVariantConfig {
  // 确定核心元数据
  const manifestVersion =
    variant.manifest?.manifest_version ||
    variant.manifestVersion ||
    config.manifest?.manifest_version ||
    config.manifestVersion ||
    (3 as ManifestVersionType);
  const defaultLocale =
    variant.manifest?.default_locale ||
    config.defaultLocale ||
    variant.manifest?.default_locale ||
    config.defaultLocale ||
    'en';
  const measurementId = variant.measurementId || config.measurementId || '';

  // 生成标识符
  const variantTarget = generateVariantTarget(
    variant.webstore,
    manifestVersion,
    variant.variantType,
  );
  const variantChannel = generateVariantChannel(variant.webstore, variant.variantType);
  const webstoreCN = WEBSTORE_CN[variant.webstore] as WebstoreCNType;

  // 浅层合并配置
  const mergedI18n = merge({}, config.i18n, variant.i18n);
  const mergedManifest = merge({}, config.manifest, variant.manifest);

  return {
    name: config.name,
    version: config.version,
    manifestVersion,
    defaultLocale,
    variantID: variant.variantId,
    variantName: variant.variantName,
    variantType: variant.variantType,
    variantChannel,
    webstoreCN,
    webstore: variant.webstore,
    webstoreId: variant.webstoreId,
    webstoreUrl: variant.webstoreUrl,
    measurementId,
    variantTarget,
    i18n: { ...mergedI18n, locales: [] }, // 保持 i18n.locales 为空数组
    manifest: {
      ...mergedManifest,
      manifest_version: manifestVersion, // 确保 manifest_version 正确
      default_locale: defaultLocale, // 确保 default_locale 正确
    },
  };
}

/**
 * TODO: 处理完整变体配置，生成所有相关路径、并进行深度合并、processI18n、processManifest 等
 */
export function processExtensionConfig(extensionName: string, variantTargets?: string[]) {}

/**
 * TODO: 生成扩展配置文件，包含 extension.config.json、i18n.json、_locales/ 等
 */
export function generateExtensionConfigFiles(extensionName: string, extensionConfig: {}) {}

/**
 * @description 在内存中处理 extension.config.ts，返回所有处理完成的基础变体配置
 * @param config 从 extension.config.ts 文件中导入的原始配置对象
 * @returns 一个以 variantTarget 为键，处理后的基础变体配置为值的对象
 */
export function defineExtensionConfig(
  config: ExtensionConfig,
): Record<string, ProcessedBaseVariantConfig> {
  logger.verbose(`开始处理扩展配置: ${config.name}`);

  const processedVariants: Record<string, ProcessedBaseVariantConfig> = {};
  const variantTargets = new Set<string>();
  for (const [index, variant] of config.variants.entries()) {
    const processedVariantConfig = processBaseVariantConfig(config, variant);

    // 验证配置
    const validation = validateBaseVariantConfig(processedVariantConfig);
    if (!validation.isValid) {
      const errorMessage = [
        `[渠道包 ${index + 1}] 配置验证失败，渠道包: "${processedVariantConfig.variantTarget}":`,
        validation.errors.join('\n'),
      ].join('\n');
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }
    // variantTarget 唯一性
    if (variantTargets.has(processedVariantConfig.variantTarget)) {
      const errorMessage = `[渠道包 ${index + 1}] 配置验证失败，重复的 variantTarget: ${processedVariantConfig.variantTarget}，请检查 webstore、manifestVersion 和 variantType 的组合`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }
    if (validation.warnings.length > 0) {
      logger.warn(
        `[渠道包 ${index + 1}] 配置验证通过，但存在警告，请检查:\n${validation.warnings.join('\n')}`,
      );
    }

    variantTargets.add(processedVariantConfig.variantTarget);
    processedVariants[processedVariantConfig.variantTarget] = processedVariantConfig;

    logger.verbose(`[渠道包 ${index + 1}] 处理渠道包配置: ${processedVariantConfig.variantTarget}`);
  }

  logger.success(
    `扩展配置处理完成: ${config.name}，共 ${Object.keys(processedVariants).length} 个渠道包`,
  );
  return {
    name: config.name,
    version: config.version,
    variants: Object.values(processedVariants),
  };
}

// #endregion
