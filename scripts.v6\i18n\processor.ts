/**
 * @fileoverview i18n 模块的核心处理器
 * @description 实现语言包发现、合并、条件化解析、格式转换等核心功能
 */

import fs from 'fs';
import path from 'path';
import { createLogger } from '../shared/logger.js';
import type {
  I18nProcessOptions,
  I18nProcessResult,
  ProcessedI18nConfig,
  LocaleMessages,
  ChromeMessage,
  ChromeLocaleMessages,
  VueI18nMessages,
  ChromeI18nMessages,
  ConditionalKeyInfo,
  PlaceholderInfo,
  MessageConversionResult,
  SUPPORTED_LOCALES,
} from './types.js';

const logger = createLogger('I18nProcessor');

// #region --- 语言包发现 ---

/**
 * @description 扫描目录获取所有可用的语言列表
 * @param localesPath 语言包目录路径
 * @returns 语言代码列表
 */
function scanLocalesDirectory(localesPath: string): string[] {
  if (!fs.existsSync(localesPath)) {
    return [];
  }

  try {
    const files = fs.readdirSync(localesPath);
    const locales = files
      .filter((file) => file.endsWith('.json'))
      .map((file) => path.basename(file, '.json'))
      .filter((locale) => SUPPORTED_LOCALES.includes(locale));

    return locales.sort();
  } catch (error) {
    logger.error(`扫描语言包目录失败 ${localesPath}: ${error}`);
    return [];
  }
}

/**
 * @description 自动发现所有可用的语言
 * @param sharedLocalesPath 共享语言包目录
 * @param extensionLocalesPath 扩展专属语言包目录
 * @returns 去重后的语言列表
 */
function discoverLocales(sharedLocalesPath: string, extensionLocalesPath: string): string[] {
  const sharedLocales = scanLocalesDirectory(sharedLocalesPath);
  const extensionLocales = scanLocalesDirectory(extensionLocalesPath);

  // 合并并去重
  const allLocales = [...new Set([...sharedLocales, ...extensionLocales])];

  logger.verbose(
    `发现语言包: 共享 ${sharedLocales.length} 个，扩展专属 ${extensionLocales.length} 个，总计 ${allLocales.length} 个`,
  );
  return allLocales;
}

// #endregion

// #region --- 语言包加载和合并 ---

/**
 * @description 加载单个语言包文件
 * @param filePath 语言包文件路径
 * @returns 语言包消息对象
 */
function loadLocaleFile(filePath: string): LocaleMessages {
  if (!fs.existsSync(filePath)) {
    return {};
  }

  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(content) as LocaleMessages;
  } catch (error) {
    logger.error(`加载语言包文件失败 ${filePath}: ${error}`);
    return {};
  }
}

/**
 * @description 合并共享和扩展专属的语言包（扩展专属优先）
 * @param locale 语言代码
 * @param sharedLocalesPath 共享语言包目录
 * @param extensionLocalesPath 扩展专属语言包目录
 * @returns 合并后的语言包消息
 */
function mergeLocaleMessages(
  locale: string,
  sharedLocalesPath: string,
  extensionLocalesPath: string,
): LocaleMessages {
  const sharedPath = path.join(sharedLocalesPath, `${locale}.json`);
  const extensionPath = path.join(extensionLocalesPath, `${locale}.json`);

  const sharedMessages = loadLocaleFile(sharedPath);
  const extensionMessages = loadLocaleFile(extensionPath);

  // 扩展专属消息优先于共享消息
  const mergedMessages = { ...sharedMessages, ...extensionMessages };

  logger.verbose(
    `合并语言包 ${locale}: 共享 ${Object.keys(sharedMessages).length} 条，扩展 ${Object.keys(extensionMessages).length} 条，合并后 ${Object.keys(mergedMessages).length} 条`,
  );
  return mergedMessages;
}

// #endregion

// #region --- 条件化文案解析 ---

/**
 * @description 解析条件化键名信息
 * @param key 键名
 * @param variantTarget 变体目标标识
 * @returns 条件化键信息
 */
function parseConditionalKey(key: string, variantTarget: string): ConditionalKeyInfo | null {
  const match = key.match(/^(.+)@(.+)$/);
  if (!match) {
    return null;
  }

  const [, baseKey, condition] = match;
  const [webstore, mv, variant] = variantTarget.split('-');

  // 计算匹配优先级
  let priority = 0;
  if (condition === variantTarget) {
    priority = 3; // 完全匹配：webstore-mv-variant
  } else if (condition === `${webstore}-${variant}`) {
    priority = 2; // webstore-variant
  } else if (condition === webstore) {
    priority = 1; // webstore
  } else {
    return null; // 不匹配
  }

  return {
    originalKey: key,
    baseKey,
    condition,
    priority,
  };
}

/**
 * @description 解析条件化文案，选择最匹配的版本
 * @param messages 原始消息对象
 * @param variantTarget 变体目标标识
 * @returns 解析后的消息对象
 */
function resolveConditionalMessages(
  messages: LocaleMessages,
  variantTarget: string,
): LocaleMessages {
  const resolved: LocaleMessages = {};
  const conditionalKeys = new Map<string, ConditionalKeyInfo[]>();

  // 分类处理键名
  Object.keys(messages).forEach((key) => {
    const conditionalInfo = parseConditionalKey(key, variantTarget);

    if (conditionalInfo) {
      // 条件化键名
      if (!conditionalKeys.has(conditionalInfo.baseKey)) {
        conditionalKeys.set(conditionalInfo.baseKey, []);
      }
      conditionalKeys.get(conditionalInfo.baseKey)!.push(conditionalInfo);
    } else {
      // 普通键名
      resolved[key] = messages[key];
    }
  });

  // 为每个基础键选择最匹配的条件化版本
  conditionalKeys.forEach((infos, baseKey) => {
    // 按优先级排序，选择优先级最高的
    infos.sort((a, b) => b.priority - a.priority);
    const bestMatch = infos[0];
    resolved[baseKey] = messages[bestMatch.originalKey];

    logger.verbose(
      `条件化文案解析: ${baseKey} 选择 ${bestMatch.originalKey} (优先级 ${bestMatch.priority})`,
    );
  });

  return resolved;
}

// #endregion

// #region --- 文案过滤 ---

/**
 * @description 根据包含和排除模式过滤文案键
 * @param messages 消息对象
 * @param includeKeys 包含模式数组
 * @param excludeKeys 排除模式数组
 * @returns 过滤后的消息对象
 */
function filterMessages(
  messages: LocaleMessages,
  includeKeys?: string[],
  excludeKeys?: string[],
): LocaleMessages {
  let filtered = { ...messages };

  // 应用包含过滤器
  if (includeKeys && includeKeys.length > 0) {
    const includeRegexes = includeKeys.map((pattern) => new RegExp(pattern));
    filtered = Object.fromEntries(
      Object.entries(filtered).filter(([key]) => includeRegexes.some((regex) => regex.test(key))),
    );
  }

  // 应用排除过滤器
  if (excludeKeys && excludeKeys.length > 0) {
    const excludeRegexes = excludeKeys.map((pattern) => new RegExp(pattern));
    filtered = Object.fromEntries(
      Object.entries(filtered).filter(([key]) => !excludeRegexes.some((regex) => regex.test(key))),
    );
  }

  return filtered;
}

// #endregion

// #region --- 格式转换 ---

/**
 * @description 提取消息中的占位符信息
 * @param message 消息内容
 * @returns 占位符信息数组
 */
function extractPlaceholders(message: string): PlaceholderInfo[] {
  const placeholderRegex = /\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g;
  const placeholders: PlaceholderInfo[] = [];
  let match;
  let index = 1;

  while ((match = placeholderRegex.exec(message)) !== null) {
    placeholders.push({
      name: match[1],
      index: index++,
      example: match[1], // 使用占位符名称作为示例
    });
  }

  return placeholders;
}

/**
 * @description 将消息转换为 Vue I18n 格式
 * @param message 原始消息
 * @returns 转换结果
 */
function convertToVueI18nFormat(message: string): MessageConversionResult {
  const placeholders = extractPlaceholders(message);

  // 将 $placeholder$ 转换为 {placeholder}
  const convertedMessage = message.replace(/\$([a-zA-Z_][a-zA-Z0-9_]*)\$/g, '{$1}');

  return {
    message: convertedMessage,
    placeholders,
  };
}

/**
 * @description 将消息转换为 Chrome 扩展格式
 * @param message 原始消息
 * @returns 转换结果
 */
function convertToChromeFormat(message: string): MessageConversionResult {
  const placeholders = extractPlaceholders(message);

  // 将 $placeholder$ 转换为 $1, $2, ...
  let convertedMessage = message;
  const chromeplaceholders: Record<string, { content: string; example?: string }> = {};

  placeholders.forEach((placeholder, index) => {
    const chromeIndex = index + 1;
    convertedMessage = convertedMessage.replace(
      new RegExp(`\\$${placeholder.name}\\$`, 'g'),
      `$${chromeIndex}`,
    );

    chromeplaceholders[placeholder.name] = {
      content: `$${chromeIndex}`,
      example: placeholder.example,
    };
  });

  return {
    message: convertedMessage,
    placeholders,
  };
}

// #endregion

// #region --- 主处理函数 ---

/**
 * @description 处理指定语言的消息，生成 Vue I18n 和 Chrome 格式
 * @param locale 语言代码
 * @param messages 原始消息对象
 * @param config i18n 配置
 * @param variantTarget 变体目标标识
 * @returns 处理后的 Vue I18n 和 Chrome 格式消息
 */
function processLocaleMessages(
  locale: string,
  messages: LocaleMessages,
  config: I18nConfig,
  variantTarget: string,
): { vueMessages: LocaleMessages; chromeMessages: ChromeLocaleMessages } {
  // 1. 解析条件化文案
  const resolvedMessages = resolveConditionalMessages(messages, variantTarget);

  // 2. 过滤文案
  const filteredMessages = filterMessages(resolvedMessages, config.includeKeys, config.excludeKeys);

  // 3. 生成 Vue I18n 格式
  const vueMessages: LocaleMessages = {};
  Object.entries(filteredMessages).forEach(([key, message]) => {
    const converted = convertToVueI18nFormat(message);
    vueMessages[key] = converted.message;
  });

  // 4. 生成 Chrome 格式
  const chromeMessages: ChromeLocaleMessages = {};

  // 确定要包含在 Chrome 格式中的键
  let chromeKeys = Object.keys(filteredMessages);

  // 如果是 Chrome 专用语言，只包含指定的键
  if (config.chromeOnlyLocales?.includes(locale) && config.chromeOnlyKeys) {
    const chromeOnlyRegexes = config.chromeOnlyKeys.map((pattern) => new RegExp(pattern));
    chromeKeys = chromeKeys.filter((key) => chromeOnlyRegexes.some((regex) => regex.test(key)));
  }

  chromeKeys.forEach((key) => {
    const message = filteredMessages[key];
    const converted = convertToChromeFormat(message);

    const chromeMessage: ChromeMessage = {
      message: converted.message,
    };

    // 如果有占位符，添加 placeholders 定义
    if (converted.placeholders.length > 0) {
      chromeMessage.placeholders = {};
      converted.placeholders.forEach((placeholder) => {
        chromeMessage.placeholders![placeholder.name] = {
          content: `$${placeholder.index}`,
          example: placeholder.example,
        };
      });
    }

    chromeMessages[key] = chromeMessage;
  });

  return { vueMessages, chromeMessages };
}

/**
 * @description 处理国际化配置，生成所有语言的 Vue I18n 和 Chrome 格式消息
 * @param options 处理选项
 * @returns 处理结果
 */
export function processI18n(options: I18nProcessOptions): I18nProcessResult {
  const { extensionName, variantTarget, config, sharedLocalesPath, extensionLocalesPath } = options;

  logger.info(`开始处理国际化配置: ${extensionName} (${variantTarget})`);

  // 1. 发现所有可用语言
  const discoveredLocales = discoverLocales(sharedLocalesPath, extensionLocalesPath);

  if (discoveredLocales.length === 0) {
    logger.warn('未发现任何语言包文件');
  }

  // 2. 处理每种语言
  const vueMessages: VueI18nMessages = {};
  const chromeMessages: ChromeI18nMessages = {};
  const warnings: string[] = [];
  let totalMessages = 0;

  // 检查手动设置的 locales 字段
  if (config.locales && config.locales.length > 0) {
    warnings.push('i18n.locales 字段会被自动扫描覆盖，手动设置将被忽略');
  }

  discoveredLocales.forEach((locale) => {
    // 合并共享和扩展专属语言包
    const mergedMessages = mergeLocaleMessages(locale, sharedLocalesPath, extensionLocalesPath);

    if (Object.keys(mergedMessages).length === 0) {
      logger.warn(`语言 ${locale} 没有找到任何消息`);
      return;
    }

    // 处理消息
    const { vueMessages: localeVueMessages, chromeMessages: localeChromeMessages } =
      processLocaleMessages(locale, mergedMessages, config, variantTarget);

    // 只有在 Chrome 专用语言列表中或者不是 Chrome 专用语言时才生成 Vue I18n 格式
    if (!config.chromeOnlyLocales?.includes(locale)) {
      vueMessages[locale] = localeVueMessages;
    }

    chromeMessages[locale] = localeChromeMessages;
    totalMessages += Object.keys(mergedMessages).length;

    logger.verbose(
      `处理语言 ${locale}: Vue I18n ${Object.keys(localeVueMessages).length} 条，Chrome ${Object.keys(localeChromeMessages).length} 条`,
    );
  });

  // 3. 构建处理后的配置
  const processedConfig: ProcessedI18nConfig = {
    locales: discoveredLocales,
    includeKeys: config.includeKeys,
    excludeKeys: config.excludeKeys,
    chromeOnlyLocales: config.chromeOnlyLocales,
    chromeOnlyKeys: config.chromeOnlyKeys,
    vueMessages,
    chromeMessages,
  };

  logger.success(
    `国际化处理完成: ${discoveredLocales.length} 种语言，总计 ${totalMessages} 条消息`,
  );

  return {
    processedConfig,
    discoveredLocales,
    totalMessages,
    warnings,
  };
}

// #endregion
